# @mucfc/dependency-analyzer

集成依赖分析工具 - 分析项目中实际使用的 npm 包依赖、递归分析嵌套依赖关系、检测版本冲突

## 功能特性

1. **实际使用包分析**: 使用 depcheck 工具分析项目代码中实际引用的 npm 包
2. **嵌套依赖分析**: 递归分析这些包的所有嵌套依赖关系
3. **版本冲突检测**: 检测依赖包中是否存在版本冲突
4. **多种输出格式**: 支持控制台、JSON、YAML 格式输出
5. **可配置排除**: 支持排除特定包名和 webpack 别名

## 安装

### 全局安装
```bash
npm install -g @mucfc/dependency-analyzer
```

### 项目内安装
```bash
npm install --save-dev @mucfc/dependency-analyzer
```

## 使用方法

### 命令行使用

```bash
# 基本使用
dependency-analyzer

# 详细输出模式
dependency-analyzer --verbose

# 排除特定包
dependency-analyzer --exclude="lodash,moment"

# 输出到文件
dependency-analyzer --output-format=yaml --output-file=report.yaml
```

### 编程方式使用

```javascript
const DependencyAnalyzer = require('@mucfc/dependency-analyzer');

const analyzer = new DependencyAnalyzer({
  exclude: ['lodash', 'moment'],
  outputFormat: 'json',
  verbose: true
});

analyzer.analyze().then(() => {
  console.log('分析完成');
}).catch(error => {
  console.error('分析失败:', error);
});
```

## 配置选项

| 选项 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `exclude` | Array | `[]` | 排除的包名列表 |
| `outputFormat` | String | `'console'` | 输出格式: `console`, `json`, `yaml` |
| `outputFile` | String | `null` | 输出文件路径 |
| `verbose` | Boolean | `false` | 是否显示详细信息 |

## 命令行选项

```
-h, --help                显示帮助信息
-v, --verbose             详细输出模式
-e, --exclude <packages>  排除的包名，用逗号分隔
-f, --output-format <fmt> 输出格式 (console|json|yaml)
-o, --output-file <file>  输出文件路径
```

## 输出示例

### 控制台输出
```
🔍 开始依赖分析...

📦 阶段1: 分析实际使用的包...

🔗 阶段2: 分析嵌套依赖关系...

⚠️  阶段3: 检测版本冲突...

📊 依赖分析报告
==================================================

📈 汇总信息:
   实际使用的包数量: 15
   版本冲突数量: 2
   分析耗时: 1234ms

📦 实际使用的包:
   react (dependency)
   lodash (dependency)
   ...

⚠️  版本冲突:
   lodash:
     "4.17.15": ["react -> lodash"]
     "4.17.21": ["vue -> lodash"]
```

## 系统要求

- Node.js >= 14.0.0
- 项目需要有 `package.json` 和 `package-lock.json` 文件
- 项目需要有 `src` 目录（用于代码分析）


## 故障排除

### 常见问题

**Q: 工具报告 0 个实际使用的包**
A: 检查是否在正确的项目目录中运行，确保存在 src 目录和源代码文件

**Q: 输出文件无法生成**
A: 检查输出目录的写入权限，确保路径存在

### 调试模式

使用 `--verbose` 参数可以看到更详细的执行信息：

```bash
dependency-analyzer --verbose
```

## 更新日志

- v1.0.0: 初始版本，集成三个分析阶段
- 支持多种输出格式
- 智能过滤 webpack 别名


## 许可证

MIT
