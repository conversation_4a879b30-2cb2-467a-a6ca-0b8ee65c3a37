#!/usr/bin/env node

const minimist = require('minimist');
const pc = require('picocolors');
const DependencyAnalyzer = require('../index');

// CLI 入口
async function main() {
  const argv = minimist(process.argv.slice(2), {
    boolean: ['help', 'verbose'],
    string: ['exclude', 'output-format', 'output-file'],
    alias: {
      h: 'help',
      v: 'verbose',
      e: 'exclude',
      f: 'output-format',
      o: 'output-file'
    }
  });

  if (argv.help) {
    console.log(`
${pc.blue('依赖分析工具')}

用法: dependency-analyzer [选项]

选项:
  -h, --help                显示帮助信息
  -v, --verbose             详细输出模式
  -e, --exclude <packages>  排除的包名，用逗号分隔

  -f, --output-format <fmt> 输出格式 (console|json|yaml)
  -o, --output-file <file>  输出文件路径

示例:
  dependency-analyzer
  dependency-analyzer --verbose
  dependency-analyzer --exclude="lodash,moment" --output-format=json
  dependency-analyzer --output-format=yaml --output-file=report.yaml
    `);
    process.exit(0);
  }

  const options = {
    exclude: argv.exclude ? argv.exclude.split(',').map(s => s.trim()) : [],
    outputFormat: argv['output-format'] || 'console',
    outputFile: argv['output-file'],
    verbose: argv.verbose
  };

  const analyzer = new DependencyAnalyzer(options);
  await analyzer.analyze();
}

// 如果直接运行此文件
if (require.main === module) {
  main().catch(error => {
    console.error(pc.red('程序执行出错:'), error);
    process.exit(1);
  });
}
