{"name": "@mu/dependency-analyzer", "version": "1.0.0", "description": "集成依赖分析工具 - 分析项目中实际使用的 npm 包依赖、递归分析嵌套依赖关系、检测版本冲突", "main": "index.js", "bin": {"dependency-analyzer": "./bin/cli.js"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["dependency", "analyzer", "npm", "package", "version-conflict", "nested-dependencies"], "author": "liurihui", "license": "MIT", "dependencies": {"depcheck": "^1.4.7", "minimist": "^1.2.8", "picocolors": "^1.0.0"}, "engines": {"node": ">=14.0.0"}, "files": ["index.js", "bin/", "README.md"], "yalcSig": "41e2b1b29941b93258b62bcb190d72d8"}