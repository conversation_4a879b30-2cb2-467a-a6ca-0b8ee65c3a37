#!/usr/bin/env node

const process = require('node:process');
const { inspect } = require('node:util');
const pc = require('picocolors');
const minimist = require('minimist');
const { promises: fs } = require('fs');
const path = require('path');

/**
 * 集成依赖分析工具
 * 功能：
 * 1. 分析项目中实际使用的 npm 包依赖
 * 2. 递归分析这些包的所有嵌套依赖关系
 * 3. 检测依赖包中是否存在版本冲突
 */

class DependencyAnalyzer {
  constructor(options = {}) {
    this.options = {
      exclude: options.exclude || [],
      outputFormat: options.outputFormat || 'console', // console, json, yaml
      outputFile: options.outputFile || null,
      verbose: options.verbose || false,
      ...options
    };
    
    // webpack 别名配置（从 config/index.js 提取）
    this.webpackAliases = new Set([
      '@src', '@api', '@comp', '@constants', '@utils', '@styles', 
      '@config', '@mucfc.com', '@models', '@services', '@assets', '@types'
    ]);
    
    this.results = {
      actualUsedPackages: [],
      nestedDependencies: {},
      versionConflicts: {},
      summary: {
        totalPackages: 0,
        conflictCount: 0,
        analysisTime: null
      }
    };
  }

  /**
   * 主要分析入口
   */
  async analyze() {
    const startTime = Date.now();
    
    try {
      console.log(pc.blue('🔍 开始依赖分析...'));
      
      // 阶段1：分析实际使用的包
      console.log(pc.cyan('\n📦 阶段1: 分析实际使用的包...'));
      await this.analyzeActualUsedPackages();
      
      // 阶段2：递归分析嵌套依赖
      console.log(pc.cyan('\n🔗 阶段2: 分析嵌套依赖关系...'));
      await this.analyzeNestedDependencies();
      
      // 阶段3：检测版本冲突
      console.log(pc.cyan('\n⚠️  阶段3: 检测版本冲突...'));
      await this.detectVersionConflicts();
      
      // 生成汇总信息
      this.results.summary.analysisTime = Date.now() - startTime;
      this.results.summary.totalPackages = this.results.actualUsedPackages.length;
      this.results.summary.conflictCount = Object.keys(this.results.versionConflicts).length;
      
      // 输出结果
      await this.outputResults();
      
      console.log(pc.green(`\n✅ 分析完成！耗时: ${this.results.summary.analysisTime}ms`));
      
      // 如果有冲突，返回错误码
      if (this.results.summary.conflictCount > 0) {
        process.exit(1);
      }
      
    } catch (error) {
      console.error(pc.red('❌ 分析过程中发生错误:'));
      console.error(error.stack);
      process.exit(1);
    }
  }

  /**
   * 阶段1：分析实际使用的包
   * 使用 depcheck 工具分析项目代码中实际引用的 npm 包
   */
  async analyzeActualUsedPackages() {
    try {
      // 导入 depcheck
      const depcheck = require('depcheck');
      
      const options = {
        ignoreBinPackage: false,
        skipMissing: false,
        package: require(path.join(process.cwd(), 'package.json'))
      };

      // 只扫描 src 目录
      const srcPath = require('path').join(process.cwd(), 'src');
      const result = await depcheck(srcPath, options);

      // depcheck.using 包含实际使用的包
      const usedPackages = Object.keys(result.using).filter(pkg => {
        // 排除 webpack 别名 - 检查包名是否包含任何别名字符串
        for (const alias of this.webpackAliases) {
          if (pkg.includes(alias)) return false;
        }

        // 排除用户指定的排除包
        if (this.options.exclude.includes(pkg)) return false;

        return true;
      });
      
      this.results.actualUsedPackages = usedPackages.map(pkg => ({
        name: pkg,
        type: 'dependency'
      }));
      
      if (this.options.verbose) {
        console.log(`   找到 ${this.results.actualUsedPackages.length} 个实际使用的包`);
        this.results.actualUsedPackages.forEach(pkg => {
          console.log(`   - ${pkg.name} (${pkg.type})`);
        });
      }
      
    } catch (error) {
      throw new Error(`分析实际使用包时出错: ${error.message}`);
    }
  }

  /**
   * 阶段2：递归分析嵌套依赖
   */
  async analyzeNestedDependencies() {
    try {
      // 读取 package-lock.json 获取完整的依赖树
      const lockFile = JSON.parse(await fs.readFile('package-lock.json', 'utf8'));
      
      // 为每个实际使用的包分析其嵌套依赖
      for (const pkg of this.results.actualUsedPackages) {
        this.results.nestedDependencies[pkg.name] = this.extractNestedDependencies(
          lockFile.dependencies, 
          pkg.name
        );
      }
      
      if (this.options.verbose) {
        console.log(`   分析了 ${Object.keys(this.results.nestedDependencies).length} 个包的嵌套依赖`);
        console.log(inspect(this.results.nestedDependencies, {
          colors: true,
          depth: 3,  // null - 显示所有嵌套层级（默认只显示3层）
        }));
      }
      
    } catch (error) {
      throw new Error(`分析嵌套依赖时出错: ${error.message}`);
    }
  }

  /**
   * 递归提取嵌套依赖
   */
  extractNestedDependencies(dependencies, packageName, visited = new Set()) {
    if (!dependencies || !dependencies[packageName] || visited.has(packageName)) {
      return {};
    }

    visited.add(packageName);
    const pkg = dependencies[packageName];
    const result = {};

    // 合并 requires 和 dependencies
    const allDeps = {};

    // 首先添加 requires 中声明的依赖
    if (pkg.requires) {
      for (const [depName, version] of Object.entries(pkg.requires)) {
        allDeps[depName] = { version, source: 'requires' };
      }
    }

    // 然后添加实际安装的 dependencies，可能会覆盖 requires 中的版本
    if (pkg.dependencies) {
      for (const [depName, depInfo] of Object.entries(pkg.dependencies)) {
        allDeps[depName] = {
          version: depInfo.version,
          source: 'dependencies',
          nested: this.extractNestedDependencies(pkg.dependencies, depName, new Set(visited))
        };
      }
    }

    // 对于只在 requires 中的依赖，尝试从顶层 dependencies 中找到版本信息
    for (const [depName, depInfo] of Object.entries(allDeps)) {
      if (depInfo.source === 'requires' && dependencies[depName]) {
        result[depName] = {
          version: dependencies[depName].version,
          nested: this.extractNestedDependencies(dependencies, depName, new Set(visited))
        };
      } else if (depInfo.source === 'dependencies') {
        result[depName] = {
          version: depInfo.version,
          nested: depInfo.nested
        };
      }
    }

    return result;
  }

  /**
   * 阶段3：检测版本冲突
   * 基于阶段二分析的嵌套依赖数据
   */
  async detectVersionConflicts() {
    try {
      // 从嵌套依赖中提取所有包的版本信息
      const allPackages = this.flattenDependencies();

      // 检测版本冲突
      const conflicts = this.findVersionConflicts(allPackages);

      this.results.versionConflicts = conflicts;

      if (this.options.verbose) {
        console.log(`   发现 ${Object.keys(conflicts).length} 个版本冲突`);
      }

    } catch (error) {
      throw new Error(`检测版本冲突时出错: ${error.message}`);
    }
  }

  /**
   * 将嵌套依赖打平成扁平的包列表
   */
  flattenDependencies() {
    const allPackages = new Map(); // packageName -> Set of {version, path}

    // 遍历所有实际使用的包的嵌套依赖
    for (const [rootPackage, nestedDeps] of Object.entries(this.results.nestedDependencies)) {
      this.collectPackageVersions(nestedDeps, allPackages, [rootPackage]);
    }

    return allPackages;
  }

  /**
   * 递归收集包版本信息
   */
  collectPackageVersions(dependencies, allPackages, currentPath = []) {
    for (const [packageName, depInfo] of Object.entries(dependencies)) {
      const packagePath = [...currentPath, packageName].join(' -> ');

      // 初始化包的版本集合
      if (!allPackages.has(packageName)) {
        allPackages.set(packageName, new Set());
      }

      // 添加版本和路径信息
      allPackages.get(packageName).add({
        version: depInfo.version,
        path: packagePath
      });

      // 递归处理嵌套依赖
      if (depInfo.nested && Object.keys(depInfo.nested).length > 0) {
        this.collectPackageVersions(depInfo.nested, allPackages, [...currentPath, packageName]);
      }
    }
  }

  /**
   * 在扁平的包列表中查找版本冲突
   */
  findVersionConflicts(allPackages) {
    const conflicts = {};

    for (const [packageName, versionSet] of allPackages) {
      // 将 Set 转换为数组以便处理
      const versions = Array.from(versionSet);

      // 如果同一个包有多个不同版本，则存在冲突
      const uniqueVersions = new Set(versions.map(v => v.version));
      if (uniqueVersions.size > 1) {
        // 按版本分组
        const versionGroups = {};
        versions.forEach(({ version, path }) => {
          if (!versionGroups[version]) {
            versionGroups[version] = [];
          }
          versionGroups[version].push(path);
        });

        conflicts[packageName] = versionGroups;
      }
    }

    return conflicts;
  }

  /**
   * 查找重复依赖（基于 find-duplicate-dependencies.js 逻辑）
   */
  async findDuplicateDependencies() {
    return new Promise((resolve, reject) => {
      const npm = require('npm');

      npm.load({ production: true, json: true }, (err) => {
        if (err) return reject(err);

        npm.commands.ls([], true, (err, packageInfo, packageObj) => {
          if (err) return reject(err);

          const catalog = this.catalogDependencies(packageObj.dependencies, packageObj.name);
          const duplicatePairs = Object.entries(catalog).filter(([name, occurrences]) => occurrences.length > 1);

          resolve(Object.fromEntries(duplicatePairs));
        });
      });
    });
  }

  /**
   * 分类依赖
   */
  catalogDependencies(dependencies, path) {
    return this._catalogDependencies({}, dependencies, path);
  }

  /**
   * 递归分类依赖
   */
  _catalogDependencies(result, dependencies, path) {
    return Object.entries(dependencies || {}).reduce((acc, entry) => {
      const [name, moduleObj] = entry;

      if (!acc[name]) {
        acc[name] = [];
      }

      const isAdded = acc[name].some((pack) => pack.version === moduleObj.version);

      if (!isAdded) {
        acc[name].push({
          name,
          version: moduleObj.version,
          from: moduleObj.from,
          path
        });
      }

      if (moduleObj.dependencies) {
        return this._catalogDependencies(acc, moduleObj.dependencies, `${path}/${name}`);
      }

      return acc;
    }, result);
  }

  /**
   * 输出结果
   */
  async outputResults() {
    switch (this.options.outputFormat) {
      case 'json':
        await this.outputJSON();
        break;
      case 'yaml':
        await this.outputYAML();
        break;
      default:
        this.outputConsole();
    }
  }

  /**
   * 控制台输出
   */
  outputConsole() {
    console.log(pc.blue('\n📊 依赖分析报告'));
    console.log('='.repeat(50));
    
    // 汇总信息
    console.log(pc.yellow('\n📈 汇总信息:'));
    console.log(`   实际使用的包数量: ${this.results.summary.totalPackages}`);
    console.log(`   版本冲突数量: ${this.results.summary.conflictCount}`);
    console.log(`   分析耗时: ${this.results.summary.analysisTime}ms`);
    
    // 实际使用的包
    if (this.results.actualUsedPackages.length > 0) {
      console.log(pc.yellow('\n📦 实际使用的包:'));
      this.results.actualUsedPackages.forEach(pkg => {
        console.log(`   ${pc.green(pkg.name)} (${pkg.type})`);
      });
    }
    
    // 版本冲突
    if (Object.keys(this.results.versionConflicts).length > 0) {
      console.log(pc.red('\n⚠️  版本冲突:'));
      for (const [name, conflicts] of Object.entries(this.results.versionConflicts)) {
        console.log(`\n${pc.red(name)}:`);
        console.log(inspect(conflicts, { colors: true }));
      }
    } else {
      console.log(pc.green('\n✅ 未发现版本冲突'));
    }
  }

  /**
   * JSON 格式输出
   */
  async outputJSON() {
    const output = JSON.stringify(this.results, null, 2);
    
    if (this.options.outputFile) {
      await fs.writeFile(this.options.outputFile, output);
      console.log(pc.green(`结果已保存到: ${this.options.outputFile}`));
    } else {
      console.log(output);
    }
  }

  /**
   * YAML 格式输出
   */
  async outputYAML() {
    // 简单的 YAML 输出实现
    const yamlOutput = this.convertToYAML(this.results);
    
    if (this.options.outputFile) {
      await fs.writeFile(this.options.outputFile, yamlOutput);
      console.log(pc.green(`结果已保存到: ${this.options.outputFile}`));
    } else {
      console.log(yamlOutput);
    }
  }

  /**
   * 简单的 YAML 转换
   */
  convertToYAML(obj, indent = 0) {
    const spaces = '  '.repeat(indent);
    let yaml = '';
    
    for (const [key, value] of Object.entries(obj)) {
      if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
        yaml += `${spaces}${key}:\n${this.convertToYAML(value, indent + 1)}`;
      } else if (Array.isArray(value)) {
        yaml += `${spaces}${key}:\n`;
        value.forEach(item => {
          if (typeof item === 'object') {
            yaml += `${spaces}- \n${this.convertToYAML(item, indent + 2)}`;
          } else {
            yaml += `${spaces}- ${item}\n`;
          }
        });
      } else {
        yaml += `${spaces}${key}: ${value}\n`;
      }
    }
    
    return yaml;
  }
}

// CLI 入口
async function main() {
  const argv = minimist(process.argv.slice(2), {
    boolean: ['help', 'verbose'],
    string: ['exclude', 'output-format', 'output-file'],
    alias: {
      h: 'help',
      v: 'verbose',
      e: 'exclude',
      f: 'output-format',
      o: 'output-file'
    }
  });

  if (argv.help) {
    console.log(`
${pc.blue('依赖分析工具')}

用法: node dependency-analyzer.js [选项]

选项:
  -h, --help                显示帮助信息
  -v, --verbose             详细输出模式
  -e, --exclude <packages>  排除的包名，用逗号分隔

  -f, --output-format <fmt> 输出格式 (console|json|yaml)
  -o, --output-file <file>  输出文件路径

示例:
  node dependency-analyzer.js
  node dependency-analyzer.js --verbose
  node dependency-analyzer.js --exclude="lodash,moment" --output-format=json
  node dependency-analyzer.js --output-format=yaml --output-file=report.yaml
    `);
    process.exit(0);
  }

  const options = {
    exclude: argv.exclude ? argv.exclude.split(',').map(s => s.trim()) : [],
    outputFormat: argv['output-format'] || 'console',
    outputFile: argv['output-file'],
    verbose: argv.verbose
  };

  const analyzer = new DependencyAnalyzer(options);
  await analyzer.analyze();
}

// 如果直接运行此文件
if (require.main === module) {
  main().catch(error => {
    console.error(pc.red('程序执行出错:'), error);
    process.exit(1);
  });
}

module.exports = DependencyAnalyzer;
